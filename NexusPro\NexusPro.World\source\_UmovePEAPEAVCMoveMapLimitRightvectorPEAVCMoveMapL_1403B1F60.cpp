﻿/*
 *Function: ??$_Umove@PEAPEAVCMoveMapLimitRight@@@?$std::vector@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@std@@IEAAPEAPEAVCMoveMapLimitRight@@PEAPEAV2@00@Z
 *Address: 0x1403B1F60
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <vector>


CMoveMapLimitRight **__fastcall std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::_Umove<CMoveMapLimitRight * *>(std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *this, CMoveMapLimitRight **_First, CMoveMapLimitRight **_Last, CMoveMapLimitRight **_Ptr)
{
  __int64*v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-28h]@1
  std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *v8; // [sp+30h] [bp+8h]@1

  v8 = this;
  v4 = &v7;
  for ( i = 8; i > 0; --i )
  {
    *(DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  return stdext::_Unchecked_uninitialized_move<CMoveMapLimitRight * *,CMoveMapLimitRight * *,std::allocator<CMoveMapLimitRight *>>(
           _First,
           _Last,
           _Ptr,
           &v8->_Alval);
}


