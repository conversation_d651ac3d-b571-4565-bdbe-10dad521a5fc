﻿/*
 *Function: ?_LoadMonBlk@CMapData@@AEAA_NPEADPEAU_map_fld@@@Z
 *Address: 0x140181850
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <vector>

// External symbol declarations
extern "C" IMAGE_DOS_HEADER __ImageBase;
extern "C" DWORD off_14000003C;
extern "C" void*_delayLoadHelper2(ImgDelayDescr*pidd, void**ppfnIATEntry);
extern struct EqSukData { void*pwszEpSuk; } EqSukList[16];
extern void MyMessageBox(const char*title, const char*message);


char __fastcall CMapData::_LoadMonBlk(CMapData *this, char *pszMapCode, _map_fld *pMapFld)
{
  __int64*v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  signed __int64 v6; // rax@34
  unsigned __int8 v7; // cf@36
  unsigned __int64 v8; // rax@36
  _base_fld*v9; // rax@47
  _base_fld*v10; // rax@48
  __int64 v11; // [sp+0h] [bp-228h]@1
  void (__cdecl*pDtor)(void *); // [sp+20h] [bp-208h]@13
  int v13; // [sp+28h] [bp-200h]@23
  char pszErrMsg; // [sp+40h] [bp-1E8h]@10
  char Dest; // [sp+E0h] [bp-148h]@4
  int n; // [sp+164h] [bp-C4h]@14
  _mon_block_fld*pBlkRec; // [sp+168h] [bp-C0h]@16
  int j; // [sp+170h] [bp-B8h]@20
  bool v19; // [sp+174h] [bp-B4h]@29
  int k; // [sp+178h] [bp-B0h]@32
  int l; // [sp+17Ch] [bp-ACh]@41
  _base_fld*v22; // [sp+180h] [bp-A8h]@46
  unsigned int v23; // [sp+188h] [bp-A0h]@51
  int m; // [sp+18Ch] [bp-9Ch]@56
  _mon_active_fld*pRec; // [sp+190h] [bp-98h]@58
  _base_fld*v26; // [sp+198h] [bp-90h]@60
  int nMonRecIndex; // [sp+1A0h] [bp-88h]@62
  _mon_block*v28; // [sp+1B0h] [bp-78h]@14
  int count[2]; // [sp+1B8h] [bp-70h]@34
  CRecordData*v30; // [sp+1C0h] [bp-68h]@41
  void*v31; // [sp+1C8h] [bp-60h]@38
  _mon_active**v32; // [sp+1D0h] [bp-58h]@41
  int __n[2]; // [sp+1D8h] [bp-50h]@53
  void*v34; // [sp+1E0h] [bp-48h]@56
  void*__t; // [sp+1E8h] [bp-40h]@53
  __int64 v36; // [sp+1F0h] [bp-38h]@4
  unsigned __int64 v37; // [sp+1F8h] [bp-30h]@14
  CRecordData*v38; // [sp+200h] [bp-28h]@39
  unsigned __int64 v39; // [sp+208h] [bp-20h]@41
  void*v40; // [sp+210h] [bp-18h]@54
  unsigned __int64 v41; // [sp+218h] [bp-10h]@4
  CMapData*pMap; // [sp+230h] [bp+8h]@1
  char*v43; // [sp+238h] [bp+10h]@1
  _map_fld*v44; // [sp+240h] [bp+18h]@1

  v44 = pMapFld;
  v43 = pszMapCode;
  pMap = this;
  v3 = &v11;
  for (signed __int64 i = 136; i > 0; --i)
  {
    *(DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v36 = -2i64;
  v41 = (unsigned __int64)&v11 ^ _security_cookie;
  sprintf(&Dest, ".\\std::map\\%s\\%s.spt", pszMapCode, pszMapCode);
  if ( CDummyPosTable::LoadDummyPosition(&pMap->m_tbMonDumPos, &Dest, "*dm") )
  {
    if ( CMapData::ConvertLocalToWorldDummy(pMap, &pMap->m_tbMonDumPos, 1) )
    {
      if ( v44->m_nMapType == 2 )
      {
        pMap->m_nMonBlockNum = 0;
        result = 1;
      }
      else
      {
        sprintf(&Dest, ".\\Map\\%s\\%s-[BLOCK].dat", v43, v43);
        if ( CRecordData::ReadRecord(&pMap->m_tbMonBlk, &Dest, 0x5A4u, &pszErrMsg) )
        {
          pMap->m_nMonBlockNum = CRecordData::GetRecordNum(&pMap->m_tbMonBlk);
          if ( pMap->m_nMonBlockNum <= 200 )
          {
            v37 = pMap->m_nMonBlockNum;
            v28 = (_mon_block *)operator new[](saturated_mul(0xB8ui64, v37));
            pMap->m_pMonBlock = v28;
            for ( n = 0; n < pMap->m_nMonBlockNum; ++n )
            {
              pBlkRec = (_mon_block_fld *)CRecordData::GetRecord(&pMap->m_tbMonBlk, n);
              if ( !pBlkRec )
              {
                MyMessageBox("CMapData Error", "(_mon_block_fld*)m_tbMonBlk.GetRecord(%d) == NULL", (unsigned int)n);
                return 0;
              }
              if ( pBlkRec->m_dwDummyNum > 0xFFFF )
              {
                ((DWORD)(pDtor) = pBlkRec->m_dwDummyNum;
                MyMessageBox(
                  "CMapData Error",
                  "_LoadMonBlk(%s).. if(pBlkRec(%d)->m_dwDummyNum(%d) > 0xFFFF)",
                  v43,
                  pBlkRec->m_dwIndex);
                return 0;
              }
              pMap->m_nMonDumNum += pBlkRec->m_dwDummyNum;
              for ( j = 0; j < pBlkRec->m_dwDummyNum; ++j )
              {
                if ( !strcmp_0(pBlkRec->m_DummyInfo[j].m_strDummyCode, "0") )
                {
                  v13 = j;
                  ((DWORD)(pDtor) = pBlkRec->m_dwDummyNum;
                  CLogFile::Write(&stru_1799C8F30, "Map:%s, MonBlock:%s.. dummy(alter %d->%d)", v43, pBlkRec->m_strCode);
                  pBlkRec->m_dwDummyNum = j;
                  break;
                }
                ppPos[j] = CDummyPosTable::GetRecord(&pMap->m_tbMonDumPos, pBlkRec->m_DummyInfo[j].m_strDummyCode);
                if ( !ppPos[j] )
                {
                  pDtor = (void (__cdecl *)(void *))&pBlkRec->m_DummyInfo[j];
                  MyMessageBox(
                    "CMapData Error",
                    "Map ( %s ) blk ( %s ) m_tbMonDumPos.GetRecord(%s) == NULL",
                    v43,
                    pBlkRec->m_strCode);
                  return 0;
                }
              }
              if ( !_mon_block::SetBlock(&pMap->m_pMonBlock[n], pBlkRec, pMap, ppPos) )
              {
                pDtor = (void (__cdecl *)(void *))pBlkRec->m_strCode;
                MyMessageBox("CMapData Error", "%s: m_pMonBlock[%d].SetBlock(%s) == false", v43, (unsigned int)n);
                return 0;
              }
              v19 = 0;
              v19 = MonsterSetInfoData::IsRotateBlock(&g_MonsterSetInfoData, &pMap->m_pMonBlock[n]);
              _mon_block::SetRotateBlock(&pMap->m_pMonBlock[n], v19);
            }
            if ( v44->m_nMonsterSetFileNum <= 30 )
            {
              for ( k = 0; k < v44->m_nMonsterSetFileNum; ++k )
              {
                pMap->m_mb[k].m_nBlockNum = pMap->m_nMonBlockNum;
                *(QWORD *)count = pMap->m_nMonBlockNum;
                v6 = 176i64 * *(QWORD *)count;
                if ( !is_mul_ok(0xB0ui64, *(unsigned __int64 *)count) )
                  v6 = -1i64;
                v7 = __CFADD__(v6, 8i64);
                v8 = v6 + 8;
                if ( v7 )
                  v8 = -1i64;
                v31 = operator new[](v8);
                if ( v31 )
                {
                  *(DWORD *)v31 = count[0];
                  `eh std::vector constructor iterator'(
                    (char *)v31 + 8,
                    0xB0ui64,
                    count[0],
                    (void (__cdecl *)(void *))CRecordData::CRecordData,
                    (void (__cdecl *)(void *))CRecordData::~CRecordData);
                  v38 = (CRecordData *)((char *)v31 + 8);
                }
                else
                {
                  v38 = 0;
                }
                v30 = v38;
                pMap->m_mb[k].m_ptbMonBlock = v38;
                v39 = pMap->m_nMonBlockNum;
                v32 = (_mon_active **)operator new[](saturated_mul(8ui64, v39));
                pMap->m_mb[k].m_ppMonAct = v32;
                for ( l = 0; l < pMap->m_nMonBlockNum; ++l )
                  pMap->m_mb[k].m_ppMonAct[l] = 0;
                for ( n = 0; n < pMap->m_nMonBlockNum; ++n )
                {
                  v22 = CRecordData::GetRecord(&pMap->m_tbMonBlk, n);
                  if ( v44->m_nMapType == 1 )
                  {
                    v10 = CRecordData::GetRecord(&pMap->m_tbMonBlk, n);
                    ((DWORD)(pDtor) = k;
                    sprintf(&Dest, ".\\Map\\%s\\%s_%d.dat", v43, v10->m_strCode);
                  }
                  else
                  {
                    v9 = CRecordData::GetRecord(&pMap->m_tbMonBlk, n);
                    sprintf(&Dest, ".\\Map\\%s\\%s.dat", v43, v9->m_strCode);
                  }
                  if ( !CRecordData::ReadRecord(&pMap->m_mb[k].m_ptbMonBlock[n], &Dest, 0x5Cu, &pszErrMsg) )
                  {
                    MyMessageBox("CMapData Error", &pszErrMsg);
                    return 0;
                  }
                  v23 = CRecordData::GetRecordNum(&pMap->m_mb[k].m_ptbMonBlock[n]);
                  if ( (signed int)v23 > 300 )
                  {
                    ((DWORD)(pDtor) = 300;
                    MyMessageBox("CMapData Error", "%s: nActNum(%d) > max_act_num(%d)", v43, v23);
                    return 0;
                  }
                  *(QWORD *)__n = (signed int)v23;
                  __t = operator new[](saturated_mul(0x28ui64, (signed int)v23));
                  if ( __t )
                  {
                    `std::vector constructor iterator'(
                      __t,
                      0x28ui64,
                      __n[0],
                      (void *((__cdecl*)void *))_mon_active::_mon_active);
                    v40 = __t;
                  }
                  else
                  {
                    v40 = 0;
                  }
                  v34 = v40;
                  pMap->m_mb[k].m_ppMonAct[n] = (_mon_active *)v40;
                  for ( m = 0; m < (signed int)v23; ++m )
                  {
                    pRec = (_mon_active_fld *)CRecordData::GetRecord(&pMap->m_mb[k].m_ptbMonBlock[n], m);
                    if ( !pRec )
                    {
                      MyMessageBox(
                        "CMapData Error",
                        "(_mon_dummy_fld*)m_ptbMonDum[%d]->GetRecord(%d) == NULL",
                        (unsigned int)n,
                        (unsigned int)m);
                      return 0;
                    }
                    v26 = CRecordData::GetRecord(&stru_1799C6210, pRec->m_strCode);
                    if ( !v26 )
                    {
                      MyMessageBox(
                        "CMapData Error",
                        "(_monster_record*)g_Main.m_tblMonsterCharacter.GetRecord(%s) == NULL",
                        pRec->m_strCode);
                      return 0;
                    }
                    nMonRecIndex = v26->m_dwIndex;
                    if ( !_mon_active::SetActive(
                            &pMap->m_mb[k].m_ppMonAct[n][m],
                            pRec,
                            &pMap->m_pMonBlock[n],
                            nMonRecIndex) )
                    {
                      ((DWORD)(pDtor) = n;
                      MyMessageBox(
                        "CMapData Error",
                        "m_ppMonDummy[%d][%d].SetDummy(pRec, pPos, &m_pMonBlock[%d]) == false",
                        (unsigned int)n,
                        (unsigned int)m);
                      return 0;
                    }
                    pMap->m_nMonTotalCount += pRec->m_dwRegenLimNum;
                  }
                }
              }
              result = 1;
            }
            else
            {
              ((DWORD)(pDtor) = 30;
              MyMessageBox(
                "CMapData Error",
                "%s: ºí·°º° ¸ó½ºÅÍ ¼¼ÆÃÆÄÀÏ¼ö°¡ ¸¹´Ù.(%d > %d)",
                v43,
                v44->m_nMonsterSetFileNum);
              result = 0;
            }
          }
          else
          {
            ((DWORD)(pDtor) = 200;
            MyMessageBox("CMapData Error", "%s: m_nMonBlockNum(%d) > max_block_num(%d)", v43, pMap->m_nMonBlockNum);
            result = 0;
          }
        }
        else
        {
          pMap->m_nMonBlockNum = 0;
          result = 1;
        }
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    MyMessageBox("CMapData Error", "m_tbMonDumPos.LoadDummyPosition(%s) == false", &Dest);
    result = 0;
  }
  return result;
}


