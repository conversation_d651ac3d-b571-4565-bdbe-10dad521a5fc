﻿/*
 *Function: _std::_Tree_std::_Tmap_traits_std::basic_string_char_std::char_traits_char__std::allocator_char____AreaList_std::less_std::basic_string_char_std::char_traits_char__std::allocator_char______std::allocator_std::pair_std::basic_string_char_std::char_traits_char__std::allocator_char____const__AreaList____0___::erase_::_1_::dtor$2
 *Address: 0x140194970
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall std::_Tree_std::_Tmap_traits_std::basic_string_char_std::char_traits_char__std::allocator_char____AreaList_std::less_std::basic_string_char_std::char_traits_char__std::allocator_char______std::allocator_std::pair_std::basic_string_char_std::char_traits_char__std::allocator_char____const__AreaList____0___::erase_::_1_::dtor_2(__int64 a1, __int64 a2)
{
  if ( *((DWORD*)a2 + 144) &1 )
  {
    *((DWORD*)a2 + 144) &= 0xFFFFFFFE;
    std::_Tree<std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char>>,AreaList,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char>> const,AreaList>>,0>>::iterator::~iterator((std::_Tree<std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,AreaList,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,AreaList> >,0> >::iterator *)(a2 + 40));
  }
}


