﻿/*
 *Function: _CMoveMapLimitManager::_CMoveMapLimitManager_::_1_::dtor$0
 *Address: 0x1403A1F60
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CMoveMapLimitManager::_CMoveMapLimitManager_::_1_::dtor_0(__int64 a1, __int64 a2)
{
  CMoveMapLimitRightInfoList::~CMoveMapLimitRightInfoList(*(CMoveMapLimitRightInfoList **)(a2 + 64));
}

