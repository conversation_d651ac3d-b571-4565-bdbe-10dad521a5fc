﻿/*
 *Function: _std::vector_CMoveMapLimitRightInfo_std::allocator_CMoveMapLimitRightInfo___::_Insert_n_::_1_::dtor$1
 *Address: 0x1403B12B0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <vector>


void __fastcall std::vector_CMoveMapLimitRightInfo_std::allocator_CMoveMapLimitRightInfo___::_Insert_n_::_1_::dtor_1(__int64 a1, __int64 a2)
{
  CMoveMapLimitRightInfo::~CMoveMapLimitRightInfo((CMoveMapLimitRightInfo *)(a2 + 40));
}

