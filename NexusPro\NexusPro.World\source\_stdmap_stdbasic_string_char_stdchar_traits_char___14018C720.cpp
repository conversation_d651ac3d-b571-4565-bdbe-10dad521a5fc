﻿/*
 *Function: _std::map_std::basic_string_char_std::char_traits_char__std::allocator_char____AreaList_std::less_std::basic_string_char_std::char_traits_char__std::allocator_char______std::allocator_std::pair_std::basic_string_char_std::char_traits_char__std::allocator_char____const__AreaList_____::operator[]_::_1_::dtor$3
 *Address: 0x14018C720
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall std::map_std::basic_string_char_std::char_traits_char__std::allocator_char____AreaList_std::less_std::basic_string_char_std::char_traits_char__std::allocator_char______std::allocator_std::pair_std::basic_string_char_std::char_traits_char__std::allocator_char____const__AreaList_____::operator[]_::_1_::dtor_3(__int64 a1, __int64 a2)
{
  std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char>> const,AreaList>::~std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char>> const,AreaList>((std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,AreaList> *)(a2 + 96));
}

