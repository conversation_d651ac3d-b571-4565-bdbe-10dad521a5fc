﻿/*
 *Function: ??$_Umove@PEAPEAVCMoveMapLimitInfo@@@?$std::vector@PEAVCMoveMapLimitInfo@@V?$allocator@PEAVCMoveMapLimitInfo@@@std@@@std@@IEAAPEAPEAVCMoveMapLimitInfo@@PEAPEAV2@00@Z
 *Address: 0x1403AAF70
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <vector>


CMoveMapLimitInfo **__fastcall std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::_Umove<CMoveMapLimitInfo * *>(std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *this, CMoveMapLimitInfo **_First, CMoveMapLimitInfo **_Last, CMoveMapLimitInfo **_Ptr)
{
  __int64*v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-28h]@1
  std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *v8; // [sp+30h] [bp+8h]@1

  v8 = this;
  v4 = &v7;
  for ( i = 8; i > 0; --i )
  {
    *(DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  return stdext::_Unchecked_uninitialized_move<CMoveMapLimitInfo * *,CMoveMapLimitInfo * *,std::allocator<CMoveMapLimitInfo *>>(
           _First,
           _Last,
           _Ptr,
           &v8->_Alval);
}


