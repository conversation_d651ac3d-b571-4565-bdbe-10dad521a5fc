#!/usr/bin/env python3
"""
Comprehensive Solution Build Test
Tests all NexusPro modules after full automation deployment
"""

import os
import sys
import time
import subprocess
from pathlib import Path

class ComprehensiveBuildTester:
    def __init__(self):
        self.modules = [
            'NexusPro.Authentication',
            'NexusPro.Combat', 
            'NexusPro.Database',
            'NexusPro.Economy',
            'NexusPro.Items',
            'NexusPro.Network',
            'NexusPro.Player',
            'NexusPro.Security',
            'NexusPro.System',
            'NexusPro.World'
        ]
        
        self.msbuild_path = "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\Bin\\MSBuild.exe"
        
        self.results = {
            'successful_builds': 0,
            'failed_builds': 0,
            'total_errors': 0,
            'total_warnings': 0,
            'module_results': []
        }
    
    def test_module_build(self, module_name):
        """Test build for a specific module"""
        print(f"\n🔨 Testing {module_name}...")
        
        vcxproj_path = f"NexusPro\\{module_name}\\{module_name}.vcxproj"
        
        if not Path(vcxproj_path).exists():
            print(f"  ❌ Project file not found: {vcxproj_path}")
            return {
                'module': module_name,
                'success': False,
                'error': 'Project file not found',
                'errors': 0,
                'warnings': 0,
                'build_time': 0
            }
        
        try:
            start_time = time.time()
            
            cmd = [
                self.msbuild_path,
                vcxproj_path,
                "/p:Configuration=Debug",
                "/p:Platform=x64",
                "/v:minimal",
                "/nologo",
                "/m"  # Multi-processor build
            ]
            
            print(f"  🔧 Building {module_name}...")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=1200)  # 20 minute timeout
            
            build_time = time.time() - start_time
            
            # Count errors and warnings
            output_text = result.stdout + result.stderr
            error_count = output_text.count('error C') + output_text.count('error LNK')
            warning_count = output_text.count('warning C') + output_text.count('warning LNK')
            
            success = result.returncode == 0
            
            if success:
                print(f"  ✅ Build successful! ({build_time:.1f}s)")
                if warning_count > 0:
                    print(f"     ⚠️  {warning_count} warnings")
            else:
                print(f"  ❌ Build failed with {error_count} errors, {warning_count} warnings ({build_time:.1f}s)")
            
            return {
                'module': module_name,
                'success': success,
                'errors': error_count,
                'warnings': warning_count,
                'build_time': build_time,
                'returncode': result.returncode,
                'output_length': len(output_text)
            }
            
        except subprocess.TimeoutExpired:
            print(f"  ⏰ Build timeout after 20 minutes")
            return {
                'module': module_name,
                'success': False,
                'error': 'Build timeout',
                'errors': 0,
                'warnings': 0,
                'build_time': 1200
            }
        except Exception as e:
            print(f"  ❌ Build error: {e}")
            return {
                'module': module_name,
                'success': False,
                'error': str(e),
                'errors': 0,
                'warnings': 0,
                'build_time': 0
            }
    
    def test_solution_build(self):
        """Test building the entire solution"""
        print(f"\n🏗️  Testing Complete Solution Build...")
        
        sln_path = "NexusPro\\NexusPro.sln"
        
        if not Path(sln_path).exists():
            print(f"  ❌ Solution file not found: {sln_path}")
            return None
        
        try:
            start_time = time.time()
            
            cmd = [
                self.msbuild_path,
                sln_path,
                "/p:Configuration=Debug",
                "/p:Platform=x64",
                "/v:minimal",
                "/nologo",
                "/m"  # Multi-processor build
            ]
            
            print(f"  🔧 Building entire solution...")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=3600)  # 1 hour timeout
            
            build_time = time.time() - start_time
            
            # Count errors and warnings
            output_text = result.stdout + result.stderr
            error_count = output_text.count('error C') + output_text.count('error LNK')
            warning_count = output_text.count('warning C') + output_text.count('warning LNK')
            
            success = result.returncode == 0
            
            if success:
                print(f"  ✅ Solution build successful! ({build_time:.1f}s)")
                if warning_count > 0:
                    print(f"     ⚠️  {warning_count} warnings")
            else:
                print(f"  ❌ Solution build failed with {error_count} errors, {warning_count} warnings ({build_time:.1f}s)")
            
            return {
                'success': success,
                'errors': error_count,
                'warnings': warning_count,
                'build_time': build_time,
                'returncode': result.returncode
            }
            
        except subprocess.TimeoutExpired:
            print(f"  ⏰ Solution build timeout after 1 hour")
            return {
                'success': False,
                'error': 'Solution build timeout',
                'errors': 0,
                'warnings': 0,
                'build_time': 3600
            }
        except Exception as e:
            print(f"  ❌ Solution build error: {e}")
            return {
                'success': False,
                'error': str(e),
                'errors': 0,
                'warnings': 0,
                'build_time': 0
            }
    
    def run_comprehensive_test(self):
        """Run comprehensive build test on all modules"""
        print("🚀 Comprehensive NexusPro Solution Build Test")
        print("=" * 70)
        print("Testing all modules after full Python automation deployment")
        print()
        
        overall_start_time = time.time()
        
        # Test individual modules
        print("📋 INDIVIDUAL MODULE BUILD TESTS")
        print("-" * 50)
        
        for module in self.modules:
            result = self.test_module_build(module)
            self.results['module_results'].append(result)
            
            if result['success']:
                self.results['successful_builds'] += 1
            else:
                self.results['failed_builds'] += 1
            
            self.results['total_errors'] += result.get('errors', 0)
            self.results['total_warnings'] += result.get('warnings', 0)
        
        # Test solution build
        print(f"\n📋 COMPLETE SOLUTION BUILD TEST")
        print("-" * 50)
        
        solution_result = self.test_solution_build()
        
        overall_time = time.time() - overall_start_time
        
        # Print comprehensive summary
        print(f"\n🎉 COMPREHENSIVE BUILD TEST COMPLETE!")
        print("=" * 70)
        print(f"📊 OVERALL STATISTICS:")
        print(f"   • Total Modules: {len(self.modules)}")
        print(f"   • Successful Builds: {self.results['successful_builds']}")
        print(f"   • Failed Builds: {self.results['failed_builds']}")
        print(f"   • Total Errors: {self.results['total_errors']:,}")
        print(f"   • Total Warnings: {self.results['total_warnings']:,}")
        print(f"   • Total Test Time: {overall_time:.1f} seconds")
        
        if len(self.modules) > 0:
            success_rate = (self.results['successful_builds'] / len(self.modules)) * 100
            print(f"   • Module Success Rate: {success_rate:.1f}%")
        
        if solution_result:
            print(f"\n🏗️  SOLUTION BUILD RESULT:")
            if solution_result['success']:
                print(f"   ✅ Solution Build: SUCCESSFUL")
                print(f"   ⏱️  Build Time: {solution_result['build_time']:.1f} seconds")
                if solution_result['warnings'] > 0:
                    print(f"   ⚠️  Warnings: {solution_result['warnings']:,}")
            else:
                print(f"   ❌ Solution Build: FAILED")
                print(f"   🚫 Errors: {solution_result['errors']:,}")
                print(f"   ⚠️  Warnings: {solution_result['warnings']:,}")
        
        # Print detailed module results
        print(f"\n📋 DETAILED MODULE RESULTS:")
        print("-" * 50)
        for result in self.results['module_results']:
            status = "✅ PASS" if result['success'] else "❌ FAIL"
            errors = result.get('errors', 0)
            warnings = result.get('warnings', 0)
            build_time = result.get('build_time', 0)
            
            print(f"   {result['module']:<25} {status} ({build_time:.1f}s)")
            if errors > 0 or warnings > 0:
                print(f"   {'':<25}     Errors: {errors}, Warnings: {warnings}")
        
        return self.results

def main():
    """Main function"""
    print("🎯 NexusPro Comprehensive Build Tester")
    print("Testing all modules after massive Python automation deployment")
    print()
    
    # Check if we're in the right directory
    if not Path('NexusPro').exists():
        print("❌ Error: NexusPro directory not found. Please run from project root.")
        return 1
    
    tester = ComprehensiveBuildTester()
    results = tester.run_comprehensive_test()
    
    # Determine overall success
    if results['successful_builds'] == len(tester.modules):
        print(f"\n🎉 ALL MODULES BUILD SUCCESSFULLY!")
        print(f"🚀 PYTHON AUTOMATION DEPLOYMENT: COMPLETE SUCCESS!")
        return 0
    else:
        failed_count = results['failed_builds']
        print(f"\n⚠️  {failed_count} modules still need attention")
        print(f"🔧 Continue with targeted fixes for remaining issues")
        return 1

if __name__ == "__main__":
    sys.exit(main())
