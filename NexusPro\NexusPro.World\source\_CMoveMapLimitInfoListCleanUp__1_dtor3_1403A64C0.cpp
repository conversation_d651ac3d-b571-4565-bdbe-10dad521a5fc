﻿/*
 *Function: _CMoveMapLimitInfoList::CleanUp_::_1_::dtor$3
 *Address: 0x1403A64C0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CMoveMapLimitInfoList::CleanUp_::_1_::dtor_3(__int64 a1, __int64 a2)
{
  std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::~_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>((std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *)(a2 + 104));
}

