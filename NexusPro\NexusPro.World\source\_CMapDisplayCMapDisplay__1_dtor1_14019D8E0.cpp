﻿/*
 *Function: _CMapDisplay::CMapDisplay_::_1_::dtor$1
 *Address: 0x14019D8E0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <vector>


void __fastcall CMapDisplay::CMapDisplay_::_1_::dtor_1(__int64 a1, __int64 a2)
{
  `eh std::vector destructor iterator'(
    (void *)(*((QWORD*)a2 + 160) + 1048i64),
    0x40ui64,
    60,
    (void (__cdecl *)(void *))CCollLineDraw::~CCollLineDraw);
}


