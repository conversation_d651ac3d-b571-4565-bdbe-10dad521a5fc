﻿/*
 *Function: _CMoveMapLimitInfoPortal::_CMoveMapLimitInfoPortal_::_1_::dtor$1
 *Address: 0x1403A40F0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <vector>


void __fastcall CMoveMapLimitInfoPortal::_CMoveMapLimitInfoPortal_::_1_::dtor_1(__int64 a1, __int64 a2)
{
  std::vector<char *,std::allocator<char *>>::~std::vector<char *,std::allocator<char *>>((std::vector<char *,std::allocator<char *> > *)(*((QWORD*)a2 + 80) + 56i64));
}


