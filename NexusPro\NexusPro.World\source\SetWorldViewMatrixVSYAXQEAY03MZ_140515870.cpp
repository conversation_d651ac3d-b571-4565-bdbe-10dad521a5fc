﻿/*
 *Function: ?SetWorldViewMatrixVS@@YAXQEAY03M@Z
 *Address: 0x140515870
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <cstring>


void __fastcall SetWorldViewMatrixVS(float (*const a1)[4])
{
  struct IDirect3DDevice8*v1; // rax@1
  char Dst; // [sp+20h] [bp-C8h]@1
  char Src; // [sp+60h] [bp-88h]@1
  char v4; // [sp+A0h] [bp-48h]@1

  D3DXMatrixMultiply_0(&Src, a1, &stru_184A79A6C);
  memcpy_0(&Dst, &Src, 0x40ui64);
  D3DXMatrixTranspose_0(&v4, &Dst);
  v1 = GetD3dDevice();
  ((void (__fastcall *)(struct IDirect3DDevice8 *, QWORD, char *, signed __int64))v1->vfptr[26].AddRef)(
    v1,
    0i64,
    &v4,
    4i64);
}


