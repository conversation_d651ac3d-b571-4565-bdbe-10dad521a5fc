﻿/*
 *Function: _CMapOperation::_CMapOperation_::_1_::dtor$1
 *Address: 0x140196240
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <vector>


void __fastcall CMapOperation::_CMapOperation_::_1_::dtor_1(__int64 a1, __int64 a2)
{
  std::vector<std::pair<int,int>,std::allocator<std::pair<int,int>>>::~std::vector<std::pair<int,int>,std::allocator<std::pair<int,int>>>((std::vector<std::pair<int,int>,std::allocator<std::pair<int,int> > > *)(*((QWORD*)a2 + 96) + 48i64));
}


