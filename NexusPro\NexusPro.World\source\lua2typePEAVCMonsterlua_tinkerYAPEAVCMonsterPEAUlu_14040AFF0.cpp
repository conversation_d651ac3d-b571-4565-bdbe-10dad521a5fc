﻿/*
 *Function: ??$lua2type@PEAVCMonster@@@lua_tinker@@YAPEAVCMonster@@PEAUlua_State@@H@Z
 *Address: 0x14040AFF0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


CMonster *__fastcall lua_tinker::lua2type<CMonster *>(struct lua_State *L, int index, int a3)
{
  __int64*v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-28h]@1
  lua_tinker::lua2object<CMonster *> *v7; // [sp+30h] [bp+8h]@1

  v7 = (lua_tinker::lua2object<CMonster *> *)L;
  v3 = &v6;
  for ( i = 8; i > 0; --i )
  {
    *(DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  return lua_tinker::lua2object<CMonster *>::invoke(v7, (struct lua_State *)(unsigned int)index, a3);
}


