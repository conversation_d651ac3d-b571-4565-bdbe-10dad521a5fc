﻿/*
 *Function: ?unregist_from_map@AutominePersonal@@QEAA_NE@Z
 *Address: 0x1402DB110
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall AutominePersonal::unregist_from_map(AutominePersonal *this, char byDestroyType)
{
  __int64*v2; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  char*v5; // rax@15
  char*v6; // rax@18
  unsigned __int16 v7; // ax@19
  int v8; // eax@19
  __int64 v9; // [sp+0h] [bp-138h]@1
  CCharacter*pThrower; // [sp+20h] [bp-118h]@12
  char byCreateCode[8]; // [sp+28h] [bp-110h]@12
  CMapData*pMap; // [sp+30h] [bp-108h]@12
  unsigned __int16 wLayerIndex; // [sp+38h] [bp-100h]@12
  float*pStdPos; // [sp+40h] [bp-F8h]@12
  bool bHide; // [sp+48h] [bp-F0h]@12
  _personal_automine_uninstall_zocl v16; // [sp+58h] [bp-E0h]@6
  int j; // [sp+74h] [bp-C4h]@8
  _STORAGE_LIST::_db_con pBattery; // [sp+88h] [bp-B0h]@10
  int v19; // [sp+C4h] [bp-74h]@13
  _STORAGE_LIST::_db_con*v20; // [sp+C8h] [bp-70h]@13
  char pbyType; // [sp+D4h] [bp-64h]@19
  char v22; // [sp+D5h] [bp-63h]@19
  _personal_automine_uninstall_circle_zocl v23; // [sp+F4h] [bp-44h]@19
  char v24; // [sp+114h] [bp-24h]@19
  char v25; // [sp+115h] [bp-23h]@19
  AutominePersonal*v26; // [sp+140h] [bp+8h]@1
  char v27; // [sp+148h] [bp+10h]@1

  v27 = byDestroyType;
  v26 = this;
  v2 = &v9;
  for (signed __int64 i = 76; i > 0; --i)
  {
    *(DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v26->m_bInstalled )
  {
    _personal_automine_uninstall_zocl::_personal_automine_uninstall_zocl(&v16);
    v16.dwObjSerial = v26->m_dwObjSerial;
    v16.byActType = v27;
    v16.dwOwnerSerial = AutominePersonal::get_ownerserial(v26);
    v16.wItemSerial = v26->m_wItemSerial;
    if ( v27 && v27 != 2 )
    {
      v6 = CPlayerDB::GetCharNameA(&v26->m_pOwner->m_Param);
      CLogFile::Write(&v26->m_logProcess, "AutominePersonal::unregist_from_map(destroy) >> Name:%s", v6);
    }
    else
    {
      for ( j = 0; j < 2; ++j )
      {
        _STORAGE_LIST::_db_con::_db_con(&pBattery);
        if ( AutominePersonal::extract_battery(v26, j, &pBattery) )
        {
          if ( _STORAGE_LIST::GetIndexEmptyCon(v26->m_pOwner->m_Param.m_pStoragePtr[0]) == 255 )
          {
            bHide = 0;
            pStdPos = v26->m_fCurPos;
            wLayerIndex = v26->m_wMapLayerIndex;
            pMap = v26->m_pCurMap;
            byCreateCode[0] = 1;
            pThrower = (CCharacter *)&v26->m_pOwner->vfptr;
            CreateItemBox(&pBattery, v26->m_pOwner, 0xFFFFFFFF, 0, pThrower, 1, pMap, wLayerIndex, v26->m_fCurPos, 0);
            pMap = (CMapData *)v26->m_pOwner->m_szItemHistoryFileName;
            CMgrAvatorItemHistory::personal_amine_itemlog(
              &CPlayer::s_MgrItemHistory,
              "THROW_GROUND",
              -1,
              pBattery.m_byTableCode,
              pBattery.m_wItemIndex,
              pBattery.m_dwDur,
              (char *)pMap);
          }
          else
          {
            v19 = 0;
            LOBYTE(pThrower) = 1;
            v20 = CPlayer::Emb_AddStorage(v26->m_pOwner, 0, (_STORAGE_LIST::_storage_con *)&pBattery.m_bLoad, 0, 1);
            if ( v20 )
            {
              v16.battery[(unsigned __int8)v16.byCnt].dwDur = pBattery.m_dwDur;
              v16.battery[(unsigned __int8)v16.byCnt].wSerial = pBattery.m_wSerial;
              ++v16.byCnt;
              pMap = (CMapData *)v26->m_pOwner->m_szItemHistoryFileName;
              *(DWORD *)byCreateCode = v20->m_dwDur;
              LOWORD(pThrower) = v20->m_wItemIndex;
              CMgrAvatorItemHistory::personal_amine_itemlog(
                &CPlayer::s_MgrItemHistory,
                "MOVE_TO_INVEN",
                v20->m_byStorageIndex,
                v20->m_byTableCode,
                (unsigned __int16)pThrower,
                *(unsigned int *)byCreateCode,
                (char *)pMap);
            }
            else
            {
              v5 = CPlayerDB::GetCharNameA(&v26->m_pOwner->m_Param);
              CLogFile::Write(
                &v26->m_logSysErr,
                "AutominePersonal::unregist_from_map(recovery) >> Failed CPlayer::EmbAddStorage() >> Name:%s",
                v5);
            }
          }
        }
      }
    }
    pbyType = 14;
    v22 = 47;
    v7 = _personal_automine_uninstall_zocl::size(&v16);
    CNetProcess::LoadSendMsg(unk_1414F2088, v26->m_pOwner->m_id.wIndex, &pbyType, (char *)&v16, v7);
    *(QWORD *)byCreateCode = (char *)v26->m_pOwner + 50608;
    pThrower = (CCharacter *)v26->m_pItem;
    CMgrAvatorItemHistory::personal_amine_uninstall(
      &CPlayer::s_MgrItemHistory,
      v27,
      v26->m_dwMineCount,
      15,
      (_STORAGE_LIST::_db_con *)pThrower,
      *(char **)byCreateCode);
    _personal_automine_uninstall_circle_zocl::_personal_automine_uninstall_circle_zocl(&v23);
    v23.dwObjSerial = v26->m_dwObjSerial;
    v23.byActType = v27;
    v24 = 14;
    v25 = 65;
    v8 = _personal_automine_uninstall_circle_zocl::size(&v23);
    CGameObject::CircleReport((CGameObject *)&v26->vfptr, &v24, (char *)&v23, v8, 0);
    AP_BatterySlot::clear(v26->m_pBatterySlot);
    AP_BatterySlot::clear(v26->m_pBatterySlot + 1);
    _personal_amine_mineore_zocl::clear(&v26->m_changed_packet);
    v26->m_bDBLoad = 0;
    v26->m_bStart = 0;
    v26->m_pItem->m_bLock = 0;
    v26->m_pOwner = 0;
    v26->m_pItem = 0;
    v26->m_bInstalled = 0;
    v26->m_wItemSerial = -1;
    v26->m_byFilledSlotCnt = 0;
    result = CGameObject::Destroy((CGameObject *)&v26->vfptr);
  }
  else
  {
    result = 0;
  }
  return result;
}


