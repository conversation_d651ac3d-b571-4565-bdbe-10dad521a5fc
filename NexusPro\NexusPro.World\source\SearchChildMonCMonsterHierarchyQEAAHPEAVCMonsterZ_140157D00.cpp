﻿/*
 *Function: ?SearchChildMon@CMonsterHierarchy@@QEAAHPEAVCMonster@@@Z
 *Address: 0x140157D00
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


signed __int64 __fastcall CMonsterHierarchy::SearchChildMon(CMonsterHierarchy *this, CMonster *pMon)
{
  int*v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int j; // [sp+0h] [bp-18h]@1
  unsigned int k; // [sp+4h] [bp-14h]@7
  CMonsterHierarchy*v7; // [sp+20h] [bp+8h]@1

  v7 = this;
  v2 = (int *)&j;
  for ( i = 4; i > 0; --i )
  {
    *v2 = -858993460;
    ++v2;
  }
  if ( pMon )
  {
    for ( j = 0; j < 3; ++j )
    {
      for ( k = 0; k < 0xA; ++k )
      {
        if ( v7->m_pChildMon[j][k] == pMon )
          return 1i64;
      }
    }
  }
  return 0i64;
}


