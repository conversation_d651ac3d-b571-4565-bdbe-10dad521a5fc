﻿/*
 *Function: ?_LoadPortal@CMapData@@AEAA_NPEAD@Z
 *Address: 0x1401825B0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <vector>

// External symbol declarations
extern "C" IMAGE_DOS_HEADER __ImageBase;
extern "C" DWORD off_14000003C;
extern "C" void*_delayLoadHelper2(ImgDelayDescr*pidd, void**ppfnIATEntry);
extern struct EqSukData { void*pwszEpSuk; } EqSukList[16];
extern void MyMessageBox(const char*title, const char*message);


char __fastcall CMapData::_LoadPortal(CMapData *this, char *pszMapCode)
{
  __int64*v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@9
  __int64 v5; // [sp+0h] [bp-1B8h]@1
  char pszErrMsg; // [sp+30h] [bp-188h]@12
  char Dest; // [sp+D0h] [bp-E8h]@4
  int v8; // [sp+154h] [bp-64h]@5
  int v9; // [sp+158h] [bp-60h]@7
  int n; // [sp+15Ch] [bp-5Ch]@17
  _portal_fld*pRec; // [sp+160h] [bp-58h]@19
  _dummy_position*pDumPos; // [sp+168h] [bp-50h]@21
  int __n[2]; // [sp+178h] [bp-40h]@14
  void*v14; // [sp+180h] [bp-38h]@17
  void*__t; // [sp+188h] [bp-30h]@14
  __int64 v16; // [sp+190h] [bp-28h]@4
  void*v17; // [sp+198h] [bp-20h]@15
  unsigned __int64 v18; // [sp+1A0h] [bp-18h]@4
  CMapData*v19; // [sp+1C0h] [bp+8h]@1
  char*v20; // [sp+1C8h] [bp+10h]@1

  v20 = pszMapCode;
  v19 = this;
  v2 = &v5;
  for (signed __int64 i = 108; i > 0; --i)
  {
    *(DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v16 = -2i64;
  v18 = (unsigned __int64)&v5 ^ _security_cookie;
  sprintf(&Dest, ".\\std::map\\%s\\%s.spt", pszMapCode, pszMapCode);
  if ( !strcmp_0(&Dest, ".\\std::map\\Dungeon00\\Dungeon00.spt") )
    v8 = 0;
  if ( !strcmp_0(&Dest, ".\\std::map\\Dungeon01\\Dungeon01.spt") )
    v9 = 0;
  if ( CDummyPosTable::LoadDummyPosition(&v19->m_tbPortalDumPos, &Dest, "*dp") )
  {
    if ( CMapData::ConvertLocalToWorldDummy(v19, &v19->m_tbPortalDumPos, 0) )
    {
      sprintf(&Dest, ".\\std::map\\%s\\%s-[Portal].dat", v20, v20);
      if ( CRecordData::ReadRecord(&v19->m_tbPortal, &Dest, 0x178u, &pszErrMsg) )
      {
        v19->m_nPortalNum = CRecordData::GetRecordNum(&v19->m_tbPortal);
        *(QWORD *)__n = v19->m_nPortalNum;
        __t = operator new[](saturated_mul(0x10ui64, *(unsigned __int64 *)__n));
        if ( __t )
        {
          `std::vector constructor iterator'(__t, 0x10ui64, __n[0], (void *((__cdecl*)void *))_portal_dummy::_portal_dummy);
          v17 = __t;
        }
        else
        {
          v17 = 0;
        }
        v14 = v17;
        v19->m_pPortal = (_portal_dummy *)v17;
        for ( n = 0; n < v19->m_nPortalNum; ++n )
        {
          pRec = (_portal_fld *)CRecordData::GetRecord(&v19->m_tbPortal, n);
          if ( !pRec )
          {
            MyMessageBox(
              "CMapData-LoadPortal(LPTSTR pszMapCode) Error",
              "Map(%s): (_portal_record*)m_tbPortal.GetRecord(%d) = NULL",
              v20,
              (unsigned int)n);
            return 0;
          }
          pDumPos = CDummyPosTable::GetRecord(&v19->m_tbPortalDumPos, pRec->m_strCode);
          if ( !pDumPos )
          {
            MyMessageBox(
              "CMapData-LoadPortal(LPTSTR pszMapCode) Error",
              "Map(%s): tbPortalPos.GetRecord(%s) = NULL",
              v20,
              pRec->m_strCode);
            CLogFile::Write(&stru_1799C8F30, "Map(%s): tbPortalPos.GetRecord(%s) = NULL", v20, pRec->m_strCode);
            return 0;
          }
          if ( pRec )
          {
            if ( pDumPos )
            {
              _portal_dummy::SetDummy(&v19->m_pPortal[n], pRec, pDumPos);
              CMapData::CheckCenterPosDummy(v19, pDumPos);
            }
          }
        }
        result = 1;
      }
      else
      {
        MyMessageBox("CMapData Error", &pszErrMsg);
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    MyMessageBox("CMapData Error", "m_tbPortalDumPos.LoadDummyPosition(%s) == false", &Dest);
    result = 0;
  }
  return result;
}


