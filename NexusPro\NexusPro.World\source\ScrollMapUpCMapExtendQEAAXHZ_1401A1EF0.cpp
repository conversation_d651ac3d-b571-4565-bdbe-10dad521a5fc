﻿/*
 *Function: ?ScrollMapUp@CMapExtend@@QEAAXH@Z
 *Address: 0x1401A1EF0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CMapExtend::ScrollMapUp(CMapExtend *this, int nInterval)
{
  __int64*v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  tagPOINT bottomRight; // [sp+20h] [bp-18h]@8
  tagPOINT topLeft; // [sp+28h] [bp-10h]@8
  CMapExtend*v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v2 = &v4;
  for ( i = 12; i > 0; --i )
  {
    *(DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v7->m_bExtendMode )
  {
    if ( v7->m_ptStartMap.y - nInterval >= 0 )
    {
      v7->m_ptStartMap.y -= nInterval;
      v7->m_ptEndMap.y -= nInterval;
      v7->m_ptCenter.y -= nInterval;
    }
    else
    {
      v7->m_ptStartMap.y = 0;
      v7->m_ptEndMap.y = v7->m_sizeExtend.cy;
      v7->m_ptCenter.y = v7->m_sizeExtend.cx / 2;
    }
    bottomRight = (tagPOINT)v7->m_ptEndMap;
    topLeft = (tagPOINT)v7->m_ptStartMap;
    CRect::SetRect(&v7->m_rcExtend, topLeft, bottomRight);
  }
}


