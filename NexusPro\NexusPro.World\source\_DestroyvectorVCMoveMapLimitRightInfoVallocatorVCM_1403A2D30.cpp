﻿/*
 *Function: ?_Destroy@?$std::vector@VCMoveMapLimitRightInfo@@V?$allocator@VCMoveMapLimitRightInfo@@@std@@@std@@IEAAXPEAVCMoveMapLimitRightInfo@@0@Z
 *Address: 0x1403A2D30
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <vector>


void __fastcall std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::_Destroy(std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *this, CMoveMapLimitRightInfo *_First, CMoveMapLimitRightInfo *_Last)
{
  __int64*v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v3 = &v5;
  for ( i = 8; i > 0; --i )
  {
    *(DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  std::_Destroy_range<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>(_First, _Last, &v6->_Alval);
}


