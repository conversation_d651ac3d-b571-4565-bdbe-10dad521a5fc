﻿/*
 *Function: ?_LoadQuest@CMapData@@AEAA_NPEAD@Z
 *Address: 0x140183B60
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <vector>

// External symbol declarations
extern "C" IMAGE_DOS_HEADER __ImageBase;
extern "C" DWORD off_14000003C;
extern "C" void*_delayLoadHelper2(ImgDelayDescr*pidd, void**ppfnIATEntry);
extern struct EqSukData { void*pwszEpSuk; } EqSukList[16];
extern void MyMessageBox(const char*title, const char*message);


char __fastcall CMapData::_LoadQuest(CMapData *this, char *pszMapCode)
{
  __int64*v2; // rdi@1
  signed __int64 j; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-108h]@1
  char Dest; // [sp+30h] [bp-D8h]@4
  int i; // [sp+B4h] [bp-54h]@13
  _dummy_position*pDumPos; // [sp+B8h] [bp-50h]@15
  int __n[2]; // [sp+C8h] [bp-40h]@10
  void*v10; // [sp+D0h] [bp-38h]@13
  void*__t; // [sp+D8h] [bp-30h]@10
  __int64 v12; // [sp+E0h] [bp-28h]@4
  void*v13; // [sp+E8h] [bp-20h]@11
  unsigned __int64 v14; // [sp+F0h] [bp-18h]@4
  CMapData*v15; // [sp+110h] [bp+8h]@1

  v15 = this;
  v2 = &v5;
  for ( j = 64; j; --j )
  {
    *(DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v12 = -2i64;
  v14 = (unsigned __int64)&v5 ^ _security_cookie;
  sprintf(&Dest, ".\\std::map\\%s\\%s.spt", pszMapCode, pszMapCode);
  if ( CDummyPosTable::LoadDummyPosition(&v15->m_tbQuestDumPos, &Dest, "*dq") )
  {
    if ( CMapData::ConvertLocalToWorldDummy(v15, &v15->m_tbQuestDumPos, 0) )
    {
      v15->m_nQuestDumNum = CDummyPosTable::GetRecordNum(&v15->m_tbQuestDumPos);
      if ( v15->m_nQuestDumNum )
      {
        *(QWORD *)__n = v15->m_nQuestDumNum;
        __t = operator new[](saturated_mul(8ui64, *(unsigned __int64 *)__n));
        if ( __t )
        {
          `std::vector constructor iterator'(__t, 8ui64, __n[0], (void *((__cdecl*)void *))_quest_dummy::_quest_dummy);
          v13 = __t;
        }
        else
        {
          v13 = 0;
        }
        v10 = v13;
        v15->m_pQuestDummy = (_quest_dummy *)v13;
        for ( i = 0; i < v15->m_nQuestDumNum; ++i )
        {
          pDumPos = CDummyPosTable::GetRecord(&v15->m_tbQuestDumPos, i);
          _quest_dummy::SetDummy(&v15->m_pQuestDummy[i], pDumPos);
          CMapData::CheckCenterPosDummy(v15, pDumPos);
        }
        result = 1;
      }
      else
      {
        result = 1;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    MyMessageBox("CMapData Error", "m_tbQuestDumPos.LoadDummyPosition(%s) == false", &Dest);
    result = 0;
  }
  return result;
}


