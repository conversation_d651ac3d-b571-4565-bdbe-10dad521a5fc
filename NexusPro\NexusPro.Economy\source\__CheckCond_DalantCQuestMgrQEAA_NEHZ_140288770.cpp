/*
 *Function: ?__CheckCond_Dalant@CQuestMgr@@QEAA_NEH@Z
 *Address: 0x140288770
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


char __fastcall CQuestMgr::__CheckCond_Dalant(CQuestMgr *char byCompare, int nDalant)
{
  __int64*v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-38h]@1
  char v7; // [sp+20h] [bp-18h]@4
  CQuestMgr*v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v3 = &v6;
  for()
{
    *(DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = byCompare;
  if()
{
    if()
{
      if ( CPlayerDB::GetDalant(&v8->m_pMaster->m_Param) < nDalant )
        return 1;
    }
    else if ( v7 == 2 && CPlayerDB::GetDalant(&v8->m_pMaster->m_Param) > nDalant )
    {
      return 1;
    }
  }
  else if ( CPlayerDB::GetDalant(&v8->m_pMaster->m_Param) == nDalant )
  {
    return 1;
  }
  return 0;
}


