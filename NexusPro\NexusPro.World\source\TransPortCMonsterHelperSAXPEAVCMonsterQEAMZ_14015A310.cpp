﻿/*
 *Function: ?TransPort@CMonsterHelper@@SAXPEAVCMonster@@QEAM@Z
 *Address: 0x14015A310
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <cstring>


void __fastcall CMonsterHelper::TransPort(CMonster *mon, float *tarPos)
{
  __int64*v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // r8@4
  __int64 v5; // [sp+0h] [bp-88h]@1
  _monster_create_setdata Dst; // [sp+30h] [bp-58h]@4
  unsigned int v7; // [sp+74h] [bp-14h]@4
  CMonster*v8; // [sp+90h] [bp+8h]@1
  float*Src; // [sp+98h] [bp+10h]@1

  Src = tarPos;
  v8 = mon;
  v2 = &v5;
  for ( i = 32; i; --i )
  {
    *(DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  _monster_create_setdata::_monster_create_setdata(&Dst);
  memcpy_0(Dst.m_fStartPos, Src, 0xCui64);
  Dst.m_nLayerIndex = v8->m_wMapLayerIndex;
  Dst.m_pMap = v8->m_pCurMap;
  Dst.m_pRecordSet = v8->m_pRecordSet;
  Dst.pActiveRec = v8->m_pActiveRec;
  Dst.bDungeon = v8->m_bDungeon;
  Dst.pDumPosition = v8->m_pDumPosition;
  Dst.pParent = CMonsterHierarchy::GetParent(&v8->m_MonHierarcy);
  v7 = ((int (__fastcall *)(CMonster *))v8->vfptr->GetHP)(v8);
  Dst.bRobExp = v8->m_bRobExp;
  CMonster::Destroy(v8, 1, 0i64);
  CMonster::Create(v8, &Dst);
  LOBYTE(v4) = 1;
  ((void (__fastcall *)(CMonster *, QWORD, __int64))v8->vfptr->SetHP)(v8, v7, v4);
}


