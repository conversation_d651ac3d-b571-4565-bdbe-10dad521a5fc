﻿/*
 *Function: _std::vector_CMoveMapLimitRightInfo_std::allocator_CMoveMapLimitRightInfo___::_Assign_n_::_1_::dtor$0
 *Address: 0x1403AF740
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <vector>


void __fastcall std::vector_CMoveMapLimitRightInfo_std::allocator_CMoveMapLimitRightInfo___::_Assign_n_::_1_::dtor_0(__int64 a1, __int64 a2)
{
  CMoveMapLimitRightInfo::~CMoveMapLimitRightInfo((CMoveMapLimitRightInfo *)(a2 + 40));
}

