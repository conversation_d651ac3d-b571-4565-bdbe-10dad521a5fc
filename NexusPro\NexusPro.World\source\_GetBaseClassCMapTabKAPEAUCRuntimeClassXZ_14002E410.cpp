﻿/*
 *Function: ?_GetBaseClass@CMapTab@@KAPEAUCRuntimeClass@@XZ
 *Address: 0x14002E410
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


CRuntimeClass *__cdecl CMapTab::_GetBaseClass()
{
  __int64*v0; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1

  v0 = &v3;
  for ( i = 8; i > 0; --i )
  {
    *(DWORD *)v0 = -858993460;
    v0 = (__int64 *)((char *)v0 + 4);
  }
  return CPropertyPage::GetThisClass();
}


