﻿/*
 *Function: _std::vector_CMoveMapLimitInfo_____ptr64_std::allocator_CMoveMapLimitInfo_____ptr64___::clear_::_1_::dtor$0
 *Address: 0x1403A7AA0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <vector>


void __fastcall std::vector_CMoveMapLimitInfo_____ptr64_std::allocator_CMoveMapLimitInfo_____ptr64___::clear_::_1_::dtor_0(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::~_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>(*(std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > **)(a2 + 56));
}

