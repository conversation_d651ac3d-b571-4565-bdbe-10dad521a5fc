﻿/*
 *Function: ?Take@CGravityStoneRegener@@QEAAEPEAVCMapData@@PEAM@Z
 *Address: 0x14012EB20
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


char __fastcall CGravityStoneRegener::Take(CGravityStoneRegener *this, CMapData *pkMap, float *pfCurPos)
{
  __int64*v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-28h]@1
  CGravityStoneRegener*v7; // [sp+30h] [bp+8h]@1

  v7 = this;
  v3 = &v6;
  for ( i = 8; i > 0; --i )
  {
    *(DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( pkMap )
  {
    if ( v7->m_eState == 2 )
    {
      if ( CGravityStoneRegener::IsNearPosition(v7, pfCurPos) )
      {
        v7->m_eState = 3;
        CGravityStoneRegener::SendMsgAlterState(v7);
        result = 0;
      }
      else
      {
        result = -120;
      }
    }
    else
    {
      result = -121;
    }
  }
  else
  {
    result = 110;
  }
  return result;
}


