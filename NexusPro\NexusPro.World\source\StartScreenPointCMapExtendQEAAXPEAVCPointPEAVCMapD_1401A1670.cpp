﻿/*
 *Function: ?StartScreenPoint@CMapExtend@@QEAAXPEAVCPoint@@PEAVCMapData@@PEAVCRect@@@Z
 *Address: 0x1401A1670
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CMapExtend::StartScreenPoint(CMapExtend *this, CPoint *pt, CMapData *pMap, CRect *rcWnd)
{
  __int64*v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-38h]@1
  _bsp_info*v7; // [sp+20h] [bp-18h]@5
  CMapExtend*v8; // [sp+40h] [bp+8h]@1
  CRect*v9; // [sp+58h] [bp+20h]@1

  v9 = rcWnd;
  v8 = this;
  v4 = &v6;
  for ( i = 12; i > 0; --i )
  {
    *(DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( !v8->m_bExtendMode )
  {
    v8->m_bSetArea = 1;
    v8->m_bMove = 0;
    v8->m_ptStartScreen = *pt;
    v8->m_ptMoveScreen = *pt;
    v7 = CMapData::GetBspInfo(pMap);
    _map_rate::Setting(&v8->m_Rate, v9->right, v9->bottom);
  }
}


