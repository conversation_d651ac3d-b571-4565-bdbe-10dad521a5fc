﻿/*
 *Function: _dynamic_atexit_destructor_for__g_MapOper__
 *Address: 0x1406E8700
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __cdecl dynamic_atexit_destructor_for__g_MapOper__()
{
  __int64*v0; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v2; // [sp+0h] [bp-28h]@1

  v0 = &v2;
  for ( i = 8; i > 0; --i )
  {
    *(DWORD *)v0 = -858993460;
    v0 = (__int64 *)((char *)v0 + 4);
  }
  CMapOperation::~CMapOperation(&g_MapOper);
}


