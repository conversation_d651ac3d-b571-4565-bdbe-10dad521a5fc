﻿/*
 *Function: ??$_Destroy_range@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@std@@YAXPEAPEAVCMoveMapLimitRight@@0AEAV?$allocator@PEAVCMoveMapLimitRight@@@0@@Z
 *Address: 0x1403A3C00
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall std::_Destroy_range<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(CMoveMapLimitRight **_First, CMoveMapLimitRight **_Last, std::allocator<CMoveMapLimitRight *> *_Al)
{
  __int64*v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  std::_Scalar_ptr_iterator_tag v6; // [sp+20h] [bp-18h]@4
  CMoveMapLimitRight**__formal; // [sp+40h] [bp+8h]@1
  CMoveMapLimitRight**_Lasta; // [sp+48h] [bp+10h]@1
  std::allocator<CMoveMapLimitRight *> *_Ala; // [sp+50h] [bp+18h]@1

  _Ala = _Al;
  _Lasta = _Last;
  __formal = _First;
  v3 = &v5;
  for ( i = 12; i > 0; --i )
  {
    *(DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v6 = std::_Ptr_cat<CMoveMapLimitRight * *,CMoveMapLimitRight * *>(&__formal, &_Lasta);
  std::_Destroy_range<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(__formal, _Lasta, _Ala, v6);
}


