﻿/*
 *Function: ?std::set@_respawn_monster_act@_dh_mission_mgr@@QEAAXPEAU__respawn_monster@@@Z
 *Address: 0x14026F0F0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall _dh_mission_mgr::_respawn_monster_act::std::set(_dh_mission_mgr::_respawn_monster_act *this, __respawn_monster *data)
{
  this->pData = data;
  this->nCum = 0;
  this->dwLastRespawnTime = 0;
  if ( !data->bCallEvent )
    this->bStart = 1;
}

