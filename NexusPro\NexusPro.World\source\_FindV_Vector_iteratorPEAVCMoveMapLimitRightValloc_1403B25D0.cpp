﻿/*
 *Function: ??$_Find@V?$_Vector_iterator@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@std@@PEAVCMoveMapLimitRight@@@std@@YA?AV?$_Vector_iterator@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@0@V10@0AEBQEAVCMoveMapLimitRight@@@Z
 *Address: 0x1403B25D0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *__fastcall std::_Find<std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>,CMoveMapLimitRight *>(std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *result, std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *_First, std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *_Last, CMoveMapLimitRight *const *_Val)
{
  __int64*v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-38h]@1
  int v8; // [sp+20h] [bp-18h]@4
  __int64 v9; // [sp+28h] [bp-10h]@4
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *v10; // [sp+40h] [bp+8h]@1
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *__that; // [sp+48h] [bp+10h]@1
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *_Right; // [sp+50h] [bp+18h]@1
  CMoveMapLimitRight*const *v13; // [sp+58h] [bp+20h]@1

  v13 = _Val;
  _Right = _Last;
  __that = _First;
  v10 = result;
  v4 = &v7;
  for ( i = 12; i > 0; --i )
  {
    *(DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v9 = -2i64;
  v8 = 0;
  while ( std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::operator!=(
            (std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *)&__that->_Mycont,
            (std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *)&_Right->_Mycont)
       && *std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::operator*(__that) != *v13 )
    std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::operator++(__that);
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(
    v10,
    __that);
  v8 |= 1u;
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::~_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(__that);
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::~_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(_Right);
  return v10;
}


