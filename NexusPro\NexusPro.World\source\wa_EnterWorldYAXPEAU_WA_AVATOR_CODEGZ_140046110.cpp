﻿/*
 *Function: ?wa_EnterWorld@@YAXPEAU_WA_AVATOR_CODE@@G@Z
 *Address: 0x140046110
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall wa_EnterWorld(_WA_AVATOR_CODE *pData, unsigned __int16 wZoneIndex)
{
  __int64*v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  CPartyPlayer*v5; // [sp+20h] [bp-18h]@4
  _WA_AVATOR_CODE*pDataa; // [sp+40h] [bp+8h]@1

  pDataa = pData;
  v2 = &v4;
  for ( i = 12; i > 0; --i )
  {
    *(DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = (CPartyPlayer *)((char *)&g_PartyPlayer + 128 * (unsigned __int64)pDataa->m_id.wIndex);
  if ( !v5->m_bLogin )
    CPartyPlayer::EnterWorld(v5, pDataa, wZoneIndex);
}


