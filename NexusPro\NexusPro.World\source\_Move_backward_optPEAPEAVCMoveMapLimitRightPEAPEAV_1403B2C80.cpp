﻿/*
 *Function: ??$_Move_backward_opt@PEAPEAVCMoveMapLimitRight@@PEAPEAV1@Urandom_access_iterator_tag@std@@U_Undefined_move_tag@3@@std@@YAPEAPEAVCMoveMapLimitRight@@PEAPEAV1@00Urandom_access_iterator_tag@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 *Address: 0x1403B2C80
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <cstring>


CMoveMapLimitRight **__fastcall std::_Move_backward_opt<CMoveMapLimitRight * *,CMoveMapLimitRight * *,std::random_access_iterator_tag,std::_Undefined_move_tag>(CMoveMapLimitRight **_First, CMoveMapLimitRight **_Last, CMoveMapLimitRight **_Dest, std::random_access_iterator_tag _First_dest_cat, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  __int64*v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-48h]@1
  std::_Range_checked_iterator_tag v10; // [sp+30h] [bp-18h]@4
  std::_Scalar_ptr_iterator_tag v11; // [sp+31h] [bp-17h]@4
  CMoveMapLimitRight**__formala; // [sp+50h] [bp+8h]@1
  CMoveMapLimitRight**_Lasta; // [sp+58h] [bp+10h]@1
  CMoveMapLimitRight**_Desta; // [sp+60h] [bp+18h]@1
  std::input_iterator_tag v15; // [sp+68h] [bp+20h]@1

  v15 = _First_dest_cat.0;
  _Desta = _Dest;
  _Lasta = _Last;
  __formala = _First;
  v6 = &v9;
  for ( i = 16; i > 0; --i )
  {
    *(DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  memset(&v10, 0, sizeof(v10));
  v11 = std::_Ptr_cat<CMoveMapLimitRight * *,CMoveMapLimitRight * *>(&__formala, &_Desta);
  return std::_Copy_backward_opt<CMoveMapLimitRight * *,CMoveMapLimitRight * *,std::random_access_iterator_tag>(
           __formala,
           _Lasta,
           _Desta,
           (std::random_access_iterator_tag)v15,
           v11,
           v10);
}


