@echo off
echo ========================================
echo NexusPro Massive Automation Deployment
echo ========================================
echo.

echo Step 1: Checking automation infrastructure...
if not exist "mass_fix_syntax.py" (
    echo ERROR: mass_fix_syntax.py not found
    pause
    exit /b 1
)

if not exist "apply_automation_to_all_modules.py" (
    echo ERROR: apply_automation_to_all_modules.py not found
    pause
    exit /b 1
)

echo ✅ All automation scripts found

echo.
echo Step 2: Checking NexusPro solution structure...
if not exist "NexusPro\NexusPro.sln" (
    echo ERROR: NexusPro solution not found
    pause
    exit /b 1
)

echo ✅ NexusPro solution structure verified

echo.
echo Step 3: Running Direct-Fix PowerShell script on Economy module...
powershell -ExecutionPolicy Bypass -File "Direct-Fix.ps1"

echo.
echo Step 4: Testing Core module build...
set MSBUILD="C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
if exist %MSBUILD% (
    echo Building Core module...
    %MSBUILD% "NexusPro\NexusPro.Core\NexusPro.Core.vcxproj" /p:Configuration=Debug /p:Platform=x64 /v:minimal /nologo
    if %ERRORLEVEL% EQU 0 (
        echo ✅ Core module build SUCCESS!
    ) else (
        echo ❌ Core module build failed
    )
) else (
    echo MSBuild not found at expected location
)

echo.
echo Step 5: Testing Economy module build...
if exist %MSBUILD% (
    echo Building Economy module...
    %MSBUILD% "NexusPro\NexusPro.Economy\NexusPro.Economy.vcxproj" /p:Configuration=Debug /p:Platform=x64 /v:minimal /nologo
    if %ERRORLEVEL% EQU 0 (
        echo ✅ Economy module build SUCCESS!
    ) else (
        echo ❌ Economy module build failed - this is expected, continuing...
    )
)

echo.
echo Step 6: Running comprehensive PowerShell automation...
powershell -ExecutionPolicy Bypass -File "Complete-All-Modules-Fixed.ps1" -BatchSize 25 -TestBuild:$true

echo.
echo ========================================
echo Massive Automation Deployment Complete
echo ========================================
echo.
echo Check the output above for results.
echo Press any key to continue...
pause
