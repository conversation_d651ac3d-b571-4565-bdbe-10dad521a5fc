﻿/*
 *Function: ??$_Fill@PEAPEAVCMoveMapLimitInfo@@PEAV1@@std@@YAXPEAPEAVCMoveMapLimitInfo@@0AEBQEAV1@@Z
 *Address: 0x1403AB9A0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall std::_Fill<CMoveMapLimitInfo * *,CMoveMapLimitInfo *>(CMoveMapLimitInfo **_First, CMoveMapLimitInfo **_Last, CMoveMapLimitInfo *const *_Val)
{
  CMoveMapLimitInfo**i; // [sp+10h] [bp+8h]@1

  for ( i = _First; i != _Last; ++i )
    *i = *_Val;
}

