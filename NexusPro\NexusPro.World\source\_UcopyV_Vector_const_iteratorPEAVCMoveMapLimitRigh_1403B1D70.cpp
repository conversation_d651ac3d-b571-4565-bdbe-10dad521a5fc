﻿/*
 *Function: ??$_Ucopy@V?$_Vector_const_iterator@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@std@@@?$std::vector@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@std@@IEAAPEAPEAVCMoveMapLimitRight@@V?$_Vector_const_iterator@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@1@0PEAPEAV2@@Z
 *Address: 0x1403B1D70
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <vector>


CMoveMapLimitRight **__fastcall std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::_Ucopy<std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>>(std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *this, std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *_First, std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *_Last, CMoveMapLimitRight **_Ptr)
{
  __int64*v4; // rdi@1
  signed __int64 i; // rcx@1
  std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *v6; // rax@4
  std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *v7; // rax@4
  __int64 v9; // [sp+0h] [bp-98h]@1
  CMoveMapLimitRight**v10; // [sp+20h] [bp-78h]@4
  char v11; // [sp+28h] [bp-70h]@4
  std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *v12; // [sp+40h] [bp-58h]@4
  char v13; // [sp+48h] [bp-50h]@4
  std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *v14; // [sp+60h] [bp-38h]@4
  __int64 v15; // [sp+68h] [bp-30h]@4
  std::allocator<CMoveMapLimitRight *> *v16; // [sp+70h] [bp-28h]@4
  std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *v17; // [sp+78h] [bp-20h]@4
  std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *v18; // [sp+80h] [bp-18h]@4
  std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *v19; // [sp+88h] [bp-10h]@4
  std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *v20; // [sp+A0h] [bp+8h]@1
  std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *v21; // [sp+A8h] [bp+10h]@1
  std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *__that; // [sp+B0h] [bp+18h]@1
  CMoveMapLimitRight**v23; // [sp+B8h] [bp+20h]@1

  v23 = _Ptr;
  __that = _Last;
  v21 = _First;
  v20 = this;
  v4 = &v9;
  for (signed __int64 i = 36; i > 0; --i)
  {
    *(DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v15 = -2i64;
  v12 = (std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *)&v11;
  v14 = (std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *)&v13;
  v16 = &v20->_Alval;
  std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(
    (std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *)&v11,
    _Last);
  v17 = v6;
  v18 = v6;
  std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(
    v14,
    v21);
  v19 = v7;
  v10 = stdext::unchecked_uninitialized_copy<std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>,CMoveMapLimitRight * *,std::allocator<CMoveMapLimitRight *>>(
          v7,
          v18,
          v23,
          v16);
  std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::~_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(v21);
  std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::~_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(__that);
  return v10;
}


