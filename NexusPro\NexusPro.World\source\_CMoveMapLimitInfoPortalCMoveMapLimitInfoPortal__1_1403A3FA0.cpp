﻿/*
 *Function: _CMoveMapLimitInfoPortal::CMoveMapLimitInfoPortal_::_1_::dtor$0
 *Address: 0x1403A3FA0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CMoveMapLimitInfoPortal::CMoveMapLimitInfoPortal_::_1_::dtor_0(__int64 a1, __int64 a2)
{
  CMoveMapLimitInfo::~CMoveMapLimitInfo(*(CMoveMapLimitInfo **)(a2 + 64));
}

