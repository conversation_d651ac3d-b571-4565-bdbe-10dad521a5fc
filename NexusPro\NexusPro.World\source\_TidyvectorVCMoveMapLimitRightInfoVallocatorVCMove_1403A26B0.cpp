﻿/*
 *Function: ?_Tidy@?$std::vector@VCMoveMapLimitRightInfo@@V?$allocator@VCMoveMapLimitRightInfo@@@std@@@std@@IEAAXXZ
 *Address: 0x1403A26B0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <vector>


void __fastcall std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::_Tidy(std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *this)
{
  __int64*v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8; i > 0; --i )
  {
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v4->_Myfirst )
  {
    std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::_Destroy(v4, v4->_Myfirst, v4->_Mylast);
    std::allocator<CMoveMapLimitRightInfo>::deallocate(
      &v4->_Alval,
      v4->_Myfirst,
      (unsigned int)((char *)v4->_Myend - (char *)v4->_Myfirst) / 40i64);
  }
  v4->_Myfirst = 0;
  v4->_Mylast = 0;
  v4->_Myend = 0;
}


