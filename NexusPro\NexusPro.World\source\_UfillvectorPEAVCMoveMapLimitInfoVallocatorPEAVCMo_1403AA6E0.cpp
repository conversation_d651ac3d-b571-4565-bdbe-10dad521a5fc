﻿/*
 *Function: ?_Ufill@?$std::vector@PEAVCMoveMapLimitInfo@@V?$allocator@PEAVCMoveMapLimitInfo@@@std@@@std@@IEAAPEAPEAVCMoveMapLimitInfo@@PEAPEAV3@_KAEBQEAV3@@Z
 *Address: 0x1403AA6E0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <vector>


CMoveMapLimitInfo **__fastcall std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::_Ufill(std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *this, CMoveMapLimitInfo **_Ptr, unsigned __int64 _Count, CMoveMapLimitInfo *const *_Val)
{
  __int64*v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-28h]@1
  std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *v8; // [sp+30h] [bp+8h]@1
  CMoveMapLimitInfo**_First; // [sp+38h] [bp+10h]@1
  unsigned __int64 _Counta; // [sp+40h] [bp+18h]@1

  _Counta = _Count;
  _First = _Ptr;
  v8 = this;
  v4 = &v7;
  for ( i = 8; i > 0; --i )
  {
    *(DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  stdext::unchecked_uninitialized_fill_n<CMoveMapLimitInfo * *,unsigned __int64,CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>(
    _Ptr,
    _Count,
    _Val,
    &v8->_Alval);
  return &_First[_Counta];
}


