﻿/*
 *Function: _std::_Construct_CMoveMapLimitRightInfo_CMoveMapLimitRightInfo__::_1_::dtor$0
 *Address: 0x1403B3850
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall std::_Construct_CMoveMapLimitRightInfo_CMoveMapLimitRightInfo__::_1_::dtor_0(__int64 a1, __int64 a2)
{
  operator delete(*(void **)(a2 + 48), *(void **)(a2 + 32));
}

