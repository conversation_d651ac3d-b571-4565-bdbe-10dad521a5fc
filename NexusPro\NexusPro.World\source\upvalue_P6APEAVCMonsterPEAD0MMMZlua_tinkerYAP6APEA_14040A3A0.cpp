﻿/*
 *Function: ??$upvalue_@P6APEAVCMonster@@PEAD0MMM@Z@lua_tinker@@YAP6APEAVCMonster@@PEAD0MMM@ZPEAUlua_State@@@Z
 *Address: 0x14040A3A0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


CMonster *(__cdecl *__fastcall lua_tinker::upvalue_<CMonster * (*)(char *,char *,float,float,float)>(struct lua_State *L, __int64 a2, int a3))(char *, char *, float, float, float)
{
  __int64*v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-28h]@1
  lua_tinker::user2type<CMonster *((__cdecl*)char *,char *,float,float,float)> *v7; // [sp+30h] [bp+8h]@1

  v7 = (lua_tinker::user2type<CMonster *((__cdecl*)char *,char *,float,float,float)> *)L;
  v3 = &v6;
  for ( i = 8; i > 0; --i )
  {
    *(DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  return lua_tinker::user2type<CMonster * (*)(char *,char *,float,float,float)>::invoke(
           v7,
           (struct lua_State *)0xFFFFD8ED,
           a3);
}


