﻿/*
 *Function: ??$_Construct@VCMoveMapLimitRightInfo@@V1@@std@@YAXPEAVCMoveMapLimitRightInfo@@AEBV1@@Z
 *Address: 0x1403B37C0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall std::_Construct<CMoveMapLimitRightInfo,CMoveMapLimitRightInfo>(CMoveMapLimitRightInfo *_Ptr, CMoveMapLimitRightInfo *_Val)
{
  __int64*v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-58h]@1
  void*_Where; // [sp+20h] [bp-38h]@4
  CMoveMapLimitRightInfo*v6; // [sp+30h] [bp-28h]@4
  __int64 v7; // [sp+38h] [bp-20h]@4
  CMoveMapLimitRightInfo*v8; // [sp+60h] [bp+8h]@1
  CMoveMapLimitRightInfo*__that; // [sp+68h] [bp+10h]@1

  __that = _Val;
  v8 = _Ptr;
  v2 = &v4;
  for ( i = 20; i; --i )
  {
    *(DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v7 = -2i64;
  _Where = v8;
  v6 = (CMoveMapLimitRightInfo *)operator new(0x28ui64, v8);
  if ( v6 )
    CMoveMapLimitRightInfo::CMoveMapLimitRightInfo(v6, __that);
}


