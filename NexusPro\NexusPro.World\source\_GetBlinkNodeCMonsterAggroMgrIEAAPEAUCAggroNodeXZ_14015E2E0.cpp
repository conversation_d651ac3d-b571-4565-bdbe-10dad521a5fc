﻿/*
 *Function: ?_GetBlinkNode@CMonsterAggroMgr@@IEAAPEAUCAggroNode@@XZ
 *Address: 0x14015E2E0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


CAggroNode *__fastcall CMonsterAggroMgr::_GetBlinkNode(CMonsterAggroMgr *this)
{
  __int64*v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  CMonsterAggroMgr*v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 12; i > 0; --i )
  {
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; j < 10; ++j )
  {
    if ( !CAggroNode::IsLive(&v6->m_AggroPool[j]) )
      return &v6->m_AggroPool[j];
  }
  return 0i64;
}


