﻿/*
 *Function: ?ReleaseLightMap@@YAXXZ
 *Address: 0x140502480
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <cstdlib>


void ReleaseLightMap(void)
{
  char*v0; // rcx@3
  int v1; // edi@4
  __int64 v2; // rbx@5

  if ( LightmapTexID )
  {
    Dfree(LightmapTexID);
    LightmapTexID = 0;
    qword_184A79DA0 = 0;
  }
  v0 = (char *)stLightmap;
  if ( stLightmap )
  {
    v1 = 0;
    if ( dword_184A79D88 > 0 )
    {
      v2 = 0;
      while ( 1 )
      {
        Dfree(*(void **)(*(QWORD *)&v0[v2] + 8i64));
        Dfree(*(void **)((char *)stLightmap + v2));
        ++v1;
        v2 += 8i64;
        if ( v1 >= dword_184A79D88 )
          break;
        v0 = (char *)stLightmap;
      }
      v0 = (char *)stLightmap;
    }
    dword_184A79D88 = 0;
    Dfree(v0);
    stLightmap = 0;
  }
}


