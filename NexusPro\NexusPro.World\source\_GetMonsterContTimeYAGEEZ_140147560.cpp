﻿/*
 *Function: ?_GetMonsterContTime@@YAGEE@Z
 *Address: 0x140147560
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


__int64 __fastcall _GetMonsterContTime(char byEffectCode, char byLv)
{
  __int64*v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  char v6; // [sp+30h] [bp+8h]@1

  v6 = byEffectCode;
  v2 = &v5;
  for ( i = 8; i > 0; --i )
  {
    *(DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  return (unsigned __int8)MonsterSetInfoData::GetLevelContSFTime(&g_MonsterSetInfoData, v6, byLv);
}


