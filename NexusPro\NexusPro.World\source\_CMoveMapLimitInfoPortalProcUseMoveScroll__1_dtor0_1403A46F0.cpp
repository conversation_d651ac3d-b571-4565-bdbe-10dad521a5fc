﻿/*
 *Function: _CMoveMapLimitInfoPortal::ProcUseMoveScroll_::_1_::dtor$0
 *Address: 0x1403A46F0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CMoveMapLimitInfoPortal::ProcUseMoveScroll_::_1_::dtor_0(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<char *,std::allocator<char *>>::~_Vector_iterator<char *,std::allocator<char *>>((std::_Vector_iterator<char *,std::allocator<char *> > *)(a2 + 144));
}

